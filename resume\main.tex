% !TeX TS-program = xelatex

\documentclass{resume}
\ResumeName{朱祉睿}

% 如果想插入照片，请使用以下两个库。
% \usepackage{graphicx}
% \usepackage{tikz}

\begin{document}

\ResumeContacts{
  18229395676,%
  \ResumeUrl{mailto:<EMAIL>}{<EMAIL>},%
  算法实习生,%
  26届硕士在读%
}

% 如果想插入照片，请取消此代码的注释。
% 但是默认不推荐插入照片，因为这不是简历的重点。
% 如果默认的照片插入格式不能满足你的需求，你可以尝试调整照片的大小，或者使用其他的插入照片的方法。
% 不然，也可以先渲染 PDF 简历，然后用其他工具在 PDF 上叠加照片。
% \begin{tikzpicture}[remember picture, overlay]
%   \node [anchor=north east, inner sep=1cm]  at (current page.north east) 
%      {\includegraphics[width=2cm]{image.png}};
% \end{tikzpicture}

\ResumeTitle


\section{教育背景}
\ResumeItem
[中山大学|硕士研究生]
{\ResumeUrl{https://www.sysu.edu.cn}{中山大学}}
[\textnormal{应用统计（数学学院）|} 硕士（保研）]
[2024.09—2026.06]

\ResumeItem
[南方科技大学|本科生]
{\ResumeUrl{https://www.sustech.edu.cn}{南方科技大学}}
[\textnormal{统计学（统计与数据科学学院）|} 本科]
[2020.09—2024.06]

\section{实习经历}

\ResumeItem{\ResumeUrl{https://www.kuaishou.com}{快手}}
[商业化 广告算法实习]
[2025.02—2025.05]

\textbf{项目一：搜索广告精排表单CVR模型消偏}

项目背景：搜索广告中的表单提交是关键转化路径，但现有CVR模型对用户兴趣的理解存在瓶颈。核心问题在于：模型将"落地页停留时长"（Leave Time）这一强相关信号，仅作>1s的粗粒度二分类处理，损失了大量时序信息。数据分析表明，停留时长与最终提交率呈显著正相关，但长尾时长样本稀疏，且搜索场景的样本与特征分布与主流推荐场景存在巨大差异（搜索:信息流样本比例约1:60），导致模型预估存在偏差，影响投放效率和客户转化成本。

技术方案:
\begin{itemize}
  \item \textbf{精细化时长建模与偏差网络融合：} 针对原有模型对用户停留时长信号利用粗糙、且搜索场景存在预估偏差的问题，我主导了本次迭代。引入保序回归（Ordinal Regression）：构建停留时长精细化建模辅助任务，将连续时长划分为40个分桶，并结合ESMM结构对桶间时序依赖进行建模，其输出的多层概率向量作为深度特征融入主塔，为CVR预估提供更丰富的兴趣强度信号。
  \item \textbf{Google ZILN与二分类子任务：} 主要解决的问题为：实际情况下，leave time有大量值为0的样本出现，因此单独建模一个网络预测leave time是否大于0。我额外增加了一个\textbf{"是否停留"的二分类子任务}，其预测概率作为ESMM链路的起始概率。
  \item \textbf{设计场景偏差网络（Logits Bias Network）：} 为解决搜索场景样本稀疏（与信息流场景样本比约1:60）问题，通过对信息流样本进行比例下采样（20\%）平衡多源数据贡献。同时，设计并行的偏差网络，该网络仅使用搜索Query等场景专属特征，独立学习搜索域的Logits偏差，通过门控机制动态融入主干网络，实现精准纠偏。
\end{itemize}

项目成果：闭环离线AUC +0.0005。全量上线后，搜索表单场景核心转化数提升+3.2\%，客户预期花费\textbf{+4.08\%}，显著优化了广告投放效率。

\textbf{项目二：基于残差建模的直播实时发券模型}

项目背景：QCPX 是新一代广告拍卖机制，基于平台-客户-用户三方博弈，将部分广告预算转为优惠券发放给价格敏感用户以提升曝光转化率，同时提升平台变现效率和客户跑量。

技术方案
\begin{itemize}
  \item \textbf{弹性Uplift增量建模:} 为解决多面额券的连续Treatment问题，设计并上线残差弹性网络，替代传统S/T-Learner，实时预估发券对CVR的增量价值。搭建严格因果推断框架(RCT)收集无偏数据，以AUUC指标评估模型因果效应排序能力(1.59 vs 随机0.53)。项目上线后，在内循环大盘取得预期花费+1.93\%(s), 消耗+0.73\%(s), \textbf{成本率-1.17\%(s)}的显著收益。
  \item \textbf{mROI Pacing与带约束优化:} 将发券策略抽象为带ROI约束的优化问题，运用拉格朗日对偶理论推导，将问题转化为一个可在线求解的、带超参数α的无约束优化目标。通过调节α实现对mROI的精准Pacing，实验验证α从1.25提升至1.4可使mROI从2.10提升至2.16。最终策略在保证mROI=2.14的前提下，实现了平台收益最大化。
\end{itemize}

项目成果：
项目全量上线后，在达成2.14的高边际投资回报率（mROI）的同时，为核心电商业务带来\textbf{+1.93\%的平台收入增长，并显著降低1.17\%}的广告成本，预计日均增收超百万元。


\ResumeItem{\ResumeUrl{https://www.bytedance.com}{字节跳动}}
[业务中台 大模型算法实习]
[2025.02—2025.05]

\textbf{项目一：基于 LLM 的 AI LQA功能效果调优}

背景: 字节跳动 STARLING 平台的 AI LQA 功能旨在自动进行翻译质检，但因准确率低 (基线合格率<65\%) 且误报严重，阻碍了 TikTok、PDI 等多业务线的有效应用及自动化流程。

技术方案:
\begin{itemize}
  \item \textbf{数据工程与增强：}
  \begin{enumerate}
    \item 利用平台的LQA 数据，借助 Gemini-2.5-Pro 模型及人工反馈，构造COT数据，为训练样本生成精细化的错误分析论证过程。应用 Pairwise 数据构建策略，将同一原文的参考翻译与错误翻译配对输入，强化模型对正负样本的对比学习能力。
    \item 针对复杂错误类型如词汇遗漏、词汇不当等与数据不平衡问题，采用投票机制筛选高质量 COT 数据，并显著扩增训练样本，有效攻克检测难点。
  \end{enumerate}
  \item \textbf{模型训练与优化：} 对 Qwen2.5-7B-Instruct 进行基于 LoRA 的 SFT ；优化模型输出包含判错结果及结构化推理过程的响应，提升结果可靠性。
\end{itemize}

项目成果:

核心指标突破： 成功将 AI LQA 模型的判错二分类Recall与Precision均稳定提升至 90\% 以上，远超基线水平 (<64\%)，达成项目核心优化目标。

\textbf{项目二：基于 LLM 的 AI翻译调优}

背景: 旨在直接提升特定业务场景（如 TT 话题/文档、PDI 中英翻译）的机器翻译质量。

技术方案:
\begin{itemize}
  \item \textbf{RL算法实践与奖励函数设计：} 采用 GRPO 对 Qwen2.5-7B-Instruct 进行Post-Training，设计结合 BLEU、TER 等自动化指标的句级 Reward 函数，增强其对模型优化方向的引导能力。且对比 DPO 方案进行对比？？
  \item \textbf{Agent链路搭建：} 构建LLM多阶段自主优化翻译流程，引入术语表以及风格指南，融合多维规范的Agent反思与迭代修正核心，引导及规范化译文。
\end{itemize}

项目成果:

核心指标突破： 成功搭建 RL 优化机器翻译以及Agent流程，成功将人工 LQA 分数稳定提升至 80\% 以上 ，超过基线水平 (<74\%)。


\ResumeItem{\ResumeUrl{https://www.sany.com.cn}{三一集团}}
[耕耘实验室 大模型算法实习]
[2024.09—2025.01]

项目背景：研发工业级多模态智能体系统，通过LLM实现复杂工业流程的智能决策与控制，解决传统方案人工依赖度高、规则泛化性差等痛点。

技术方案
\begin{itemize}
  \item \textbf{数据集构建：} 针对含大量无语义标注数据、动作异常等结构性问题的原始数据集，进行有效筛选与过滤；通过对视觉信息随机剪裁等方法实施数据增强。构建PromptBuilder对话管理、系统提示集成和特殊标记处理。
  \item \textbf{模型选择与微调：} 构建了包含SigLIP视觉编码器、Fused-GELU投影层及Qwen2大语言模型的多模态VLA框架。构建Action Tokenizer，创新性采用离散化映射连续动作向量至语言模型词表中Special Token处。
  \item \textbf{模型训练与评估：} 进行全参数端到端SFT，监督模型学习预测动作向量。以L1 Loss，Action Accuracy等作为训练监控指标，最终在Libero Benchmark评估模型预测、执行动作能力。
\end{itemize}

项目成果：模型成功预测动作的准确率达到86\%，内存占用降幅达67\%，有效提升公司焊接工作效率和质量。



\section{个人总结}



\end{document}
